% test_modified_algorithm.m
% 测试修改后的算法效果

clear; close all; clc;
fprintf('测试修改后的ISAR算法...\n');

% 加载数据
try
    load shipx2.mat; 
    load s_r_tm2.mat
    echo_data = s_r_tm2;
    fprintf('数据加载成功。\n');
catch
    fprintf('数据加载失败，退出测试。\n');
    return;
end

% 设置参数
sim_params = struct();
sim_params.Num_r = size(echo_data, 1);
sim_params.Num_tm = size(echo_data, 2);
sim_params.PRF = 1400;
sim_params.fc = 5.2e9;
sim_params.c = 3e8;
sim_params.B = 80e6;
delta_r_res_actual = sim_params.c / (2*sim_params.B);
r_center_actual = 0;
sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, ...
                             r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);
sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);

doppler_axis = linspace(-sim_params.PRF/2, sim_params.PRF/2, sim_params.Num_tm);

% 保存原始数据
echo_data_original = echo_data;

% 数据预处理（去均值）
fprintf('应用去均值预处理...\n');
for r_bin = 1:size(echo_data, 1)
    echo_data(r_bin, :) = echo_data(r_bin, :) - mean(echo_data(r_bin, :));
end

% 零多普勒抑制参数
zero_doppler_suppression = true;
center_suppression_threshold = 0.03;
center_suppression_factor = 0.3;
notch_width = 5;

% 应用零多普勒抑制
if zero_doppler_suppression
    fprintf('应用零多普勒抑制...\n');
    suppression_count = 0;
    
    for r_bin = 1:size(echo_data, 1)
        signal_fft = fft(echo_data(r_bin, :));
        signal_fft_abs = abs(signal_fft);
        
        N = length(signal_fft);
        center_idx = round(N/2) + 1;
        
        center_range = max(1, center_idx-notch_width):min(N, center_idx+notch_width);
        center_energy = sum(signal_fft_abs(center_range).^2);
        total_energy = sum(signal_fft_abs.^2);
        
        if center_energy > center_suppression_threshold * total_energy
            suppression_count = suppression_count + 1;
            signal_fft(center_range) = signal_fft(center_range) * center_suppression_factor;
            echo_data(r_bin, :) = ifft(signal_fft);
        end
    end
    fprintf('对 %d 个距离单元应用了零多普勒抑制\n', suppression_count);
end

% 计算FFT结果进行对比
fprintf('计算FFT结果...\n');
raw_fft_original = fftshift(fft(echo_data_original, [], 2), 2);
raw_fft_processed = fftshift(fft(echo_data, [], 2), 2);

% 显示对比结果
fprintf('显示对比结果...\n');

% 原始FFT结果
figure('Name', '原始FFT结果');
G_original = 20*log10(abs(raw_fft_original)./max(abs(raw_fft_original(:)) + eps));
imagesc(doppler_axis, sim_params.r_axis, G_original);
caxis([-40,0]);
xlabel('多普勒频率 (Hz)');
ylabel('距离 (米)');
title('原始数据FFT结果 (dB)');
colorbar;
axis xy;
colormap('jet');
hold on;
plot([0, 0], [min(sim_params.r_axis), max(sim_params.r_axis)], 'r--', 'LineWidth', 2);
legend('零多普勒线', 'Location', 'best');

% 处理后FFT结果
figure('Name', '零多普勒抑制后FFT结果');
G_processed = 20*log10(abs(raw_fft_processed)./max(abs(raw_fft_processed(:)) + eps));
imagesc(doppler_axis, sim_params.r_axis, G_processed);
caxis([-40,0]);
xlabel('多普勒频率 (Hz)');
ylabel('距离 (米)');
title('零多普勒抑制后FFT结果 (dB)');
colorbar;
axis xy;
colormap('jet');
hold on;
plot([0, 0], [min(sim_params.r_axis), max(sim_params.r_axis)], 'r--', 'LineWidth', 2);
legend('零多普勒线', 'Location', 'best');

% 分析零频分量能量
fprintf('\n=== 零频分量能量分析 ===\n');
center_idx = round(sim_params.Num_tm / 2);
center_range_analysis = max(1, center_idx-5):min(sim_params.Num_tm, center_idx+5);

center_energy_original = sum(abs(raw_fft_original(:, center_range_analysis)).^2, 'all');
total_energy_original = sum(abs(raw_fft_original(:)).^2, 'all');
center_ratio_original = center_energy_original / total_energy_original;

center_energy_processed = sum(abs(raw_fft_processed(:, center_range_analysis)).^2, 'all');
total_energy_processed = sum(abs(raw_fft_processed(:)).^2, 'all');
center_ratio_processed = center_energy_processed / total_energy_processed;

fprintf('原始数据中心频率能量比例: %.4f (%.2f%%)\n', center_ratio_original, center_ratio_original*100);
fprintf('处理后中心频率能量比例: %.4f (%.2f%%)\n', center_ratio_processed, center_ratio_processed*100);
fprintf('中心频率能量抑制比例: %.2f%%\n', (1-center_ratio_processed/center_ratio_original)*100);

% 保存处理后的数据供主算法使用
save('echo_data_processed.mat', 'echo_data', 'sim_params');
fprintf('处理后的数据已保存到 echo_data_processed.mat\n');

fprintf('测试完成。\n');
