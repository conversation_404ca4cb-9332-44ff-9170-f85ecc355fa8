% run_modified_testgemi.m
% 运行修改后的testgemi算法并保存结果

clear; close all; clc;
fprintf('运行修改后的ISAR算法...\n');

% 运行修改后的testgemi算法
try
    run('testgemi.m');
    fprintf('算法运行完成。\n');
    
    % 保存成像结果
    save('ISAR_results_modified.mat', 'ISAR_image_fused', 'dominant_mode_compensated_fft', ...
         'vmd_modes_all_bins', 'phase_coeffs_all_bins', 'admm_convergence_all_bins', ...
         'sim_params', 'doppler_axis');
    fprintf('结果已保存到 ISAR_results_modified.mat\n');
    
    % 分析亮线抑制效果
    fprintf('\n=== 亮线抑制效果分析 ===\n');
    
    % 计算中心频率附近的能量
    ISAR_image_fused_shifted = fftshift(ISAR_image_fused, 2);
    center_idx = round(size(ISAR_image_fused_shifted, 2) / 2);
    center_range = max(1, center_idx-5):min(size(ISAR_image_fused_shifted, 2), center_idx+5);
    
    center_energy = sum(abs(ISAR_image_fused_shifted(:, center_range)).^2, 'all');
    total_energy = sum(abs(ISAR_image_fused_shifted(:)).^2, 'all');
    center_ratio = center_energy / total_energy;
    
    fprintf('修改后算法中心频率能量比例: %.4f (%.2f%%)\n', center_ratio, center_ratio*100);
    
    % 计算图像质量指标
    contrast_val = std(abs(ISAR_image_fused_shifted(:))) / mean(abs(ISAR_image_fused_shifted(:)));
    entropy_val = -sum(abs(ISAR_image_fused_shifted(:)).^2 .* log2(abs(ISAR_image_fused_shifted(:)).^2 + eps), 'all', 'omitnan');
    
    fprintf('图像对比度: %.4f\n', contrast_val);
    fprintf('图像熵: %.4f\n', entropy_val);
    
    % 显示最终结果（不显示图形，只保存数据）
    fprintf('成像处理完成，结果已保存。\n');
    
catch ME
    fprintf('算法运行出错: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).file, ME.stack(1).line);
end
