% debug_spectrum.m
% 调试频谱结构，找出亮线的真正位置

clear; close all; clc;
fprintf('调试频谱结构...\n');

% 加载结果
try
    load('ISAR_results_modified.mat');
    fprintf('加载修改后的结果成功。\n');
catch
    fprintf('无法加载结果文件。\n');
    return;
end

% 分析频谱结构
fprintf('\n=== 频谱结构分析 ===\n');
fprintf('ISAR图像尺寸: %d x %d\n', size(ISAR_image_fused, 1), size(ISAR_image_fused, 2));

% 应用fftshift
ISAR_shifted = fftshift(ISAR_image_fused, 2);
fprintf('移位后图像尺寸: %d x %d\n', size(ISAR_shifted, 1), size(ISAR_shifted, 2));

% 计算能量分布
energy_per_freq = sum(abs(ISAR_shifted).^2, 1);
total_energy = sum(energy_per_freq);

% 找到能量最强的频率bin
[max_energy, max_freq_idx] = max(energy_per_freq);
fprintf('最强频率bin索引: %d，能量: %.2e\n', max_freq_idx, max_energy);
fprintf('最强频率bin能量占比: %.2f%%\n', max_energy/total_energy*100);

% 分析中心频率附近的能量
center_idx = round(size(ISAR_shifted, 2) / 2);
fprintf('理论中心频率索引: %d\n', center_idx);

% 检查不同范围的能量分布
ranges = [1, 3, 5, 7, 10];
for range_width = ranges
    range_indices = max(1, center_idx-range_width):min(size(ISAR_shifted, 2), center_idx+range_width);
    range_energy = sum(energy_per_freq(range_indices));
    range_ratio = range_energy / total_energy;
    fprintf('中心±%d bin能量占比: %.2f%%\n', range_width, range_ratio*100);
end

% 检查最强频率bin附近的能量
max_ranges = [1, 3, 5, 7, 10];
for range_width = max_ranges
    range_indices = max(1, max_freq_idx-range_width):min(size(ISAR_shifted, 2), max_freq_idx+range_width);
    range_energy = sum(energy_per_freq(range_indices));
    range_ratio = range_energy / total_energy;
    fprintf('最强bin±%d bin能量占比: %.2f%%\n', range_width, range_ratio*100);
end

% 显示频率轴信息
if exist('doppler_axis', 'var')
    fprintf('\n多普勒轴范围: %.1f Hz 到 %.1f Hz\n', min(doppler_axis), max(doppler_axis));
    fprintf('中心频率对应的多普勒频率: %.1f Hz\n', doppler_axis(center_idx));
    fprintf('最强频率对应的多普勒频率: %.1f Hz\n', doppler_axis(max_freq_idx));
else
    fprintf('\n未找到多普勒轴信息。\n');
end

% 分析每个距离单元的频谱特性
fprintf('\n=== 距离单元频谱分析 ===\n');
strong_center_count = 0;
strong_max_count = 0;

for r_idx = 1:size(ISAR_shifted, 1)
    spectrum = ISAR_shifted(r_idx, :);
    spectrum_energy = abs(spectrum).^2;
    total_r_energy = sum(spectrum_energy);
    
    if total_r_energy > 0
        % 检查中心频率能量
        center_energy = spectrum_energy(center_idx);
        center_ratio_r = center_energy / total_r_energy;
        
        % 检查最强频率能量
        max_energy_r = spectrum_energy(max_freq_idx);
        max_ratio_r = max_energy_r / total_r_energy;
        
        if center_ratio_r > 0.1  % 10%阈值
            strong_center_count = strong_center_count + 1;
        end
        
        if max_ratio_r > 0.1  % 10%阈值
            strong_max_count = strong_max_count + 1;
        end
    end
end

fprintf('中心频率能量>10%%的距离单元数: %d / %d\n', strong_center_count, size(ISAR_shifted, 1));
fprintf('最强频率能量>10%%的距离单元数: %d / %d\n', strong_max_count, size(ISAR_shifted, 1));

% 创建能量分布图
fprintf('\n生成能量分布图...\n');
figure('Name', '频率能量分布');
if exist('doppler_axis', 'var')
    plot(doppler_axis, 10*log10(energy_per_freq + eps));
    xlabel('多普勒频率 (Hz)');
else
    plot(1:length(energy_per_freq), 10*log10(energy_per_freq + eps));
    xlabel('频率bin索引');
end
ylabel('能量 (dB)');
title('各频率bin的能量分布');
grid on;
hold on;

% 标记中心频率和最强频率
if exist('doppler_axis', 'var')
    plot(doppler_axis(center_idx), 10*log10(energy_per_freq(center_idx) + eps), 'ro', 'MarkerSize', 8, 'LineWidth', 2);
    plot(doppler_axis(max_freq_idx), 10*log10(energy_per_freq(max_freq_idx) + eps), 'gs', 'MarkerSize', 8, 'LineWidth', 2);
    legend('能量分布', '理论中心频率', '实际最强频率', 'Location', 'best');
else
    plot(center_idx, 10*log10(energy_per_freq(center_idx) + eps), 'ro', 'MarkerSize', 8, 'LineWidth', 2);
    plot(max_freq_idx, 10*log10(energy_per_freq(max_freq_idx) + eps), 'gs', 'MarkerSize', 8, 'LineWidth', 2);
    legend('能量分布', '理论中心频率', '实际最强频率', 'Location', 'best');
end

% 保存调试信息
debug_info = struct();
debug_info.center_idx = center_idx;
debug_info.max_freq_idx = max_freq_idx;
debug_info.energy_per_freq = energy_per_freq;
debug_info.total_energy = total_energy;
debug_info.strong_center_count = strong_center_count;
debug_info.strong_max_count = strong_max_count;

save('debug_spectrum_info.mat', 'debug_info');
fprintf('\n调试信息已保存到 debug_spectrum_info.mat\n');

fprintf('频谱调试完成。\n');
