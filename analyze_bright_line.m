% analyze_bright_line.m
% 分析亮线产生的原因

clear; close all; clc;
fprintf('分析亮线产生原因...\n');

% 加载数据
try
    load shipx2.mat; 
    load s_r_tm2.mat
    echo_data = s_r_tm2;
    fprintf('数据加载成功。\n');
catch
    fprintf('数据加载失败，退出分析。\n');
    return;
end

% 设置参数
sim_params = struct();
sim_params.Num_r = size(echo_data, 1);
sim_params.Num_tm = size(echo_data, 2);
sim_params.PRF = 1400;
sim_params.fc = 5.2e9;
sim_params.c = 3e8;
sim_params.B = 80e6;
delta_r_res_actual = sim_params.c / (2*sim_params.B);
r_center_actual = 0;
sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, ...
                             r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);
sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);

doppler_axis = linspace(-sim_params.PRF/2, sim_params.PRF/2, sim_params.Num_tm);

% 数据预处理（去均值）
echo_data_processed = echo_data;
for r_bin = 1:size(echo_data, 1)
    echo_data_processed(r_bin, :) = echo_data_processed(r_bin, :) - mean(echo_data_processed(r_bin, :));
end

% 计算FFT
raw_fft = fftshift(fft(echo_data_processed, [], 2), 2);

% 分析频谱特性
fprintf('\n=== 频谱能量分析 ===\n');

% 找到中心频率索引（零多普勒）
center_idx = round(sim_params.Num_tm / 2);
fprintf('中心频率索引: %d (对应0 Hz)\n', center_idx);

% 分析中心频率附近的能量分布
center_range = max(1, center_idx-5):min(sim_params.Num_tm, center_idx+5);
center_energy = sum(abs(raw_fft(:, center_range)).^2, 'all');
total_energy = sum(abs(raw_fft(:)).^2, 'all');
center_ratio = center_energy / total_energy;

fprintf('中心频率±5bin能量比例: %.4f (%.2f%%)\n', center_ratio, center_ratio*100);

% 分析每个距离单元的中心频率能量
center_energy_per_range = zeros(sim_params.Num_r, 1);
for r_idx = 1:sim_params.Num_r
    center_energy_per_range(r_idx) = sum(abs(raw_fft(r_idx, center_range)).^2);
end

% 找到能量最强的距离单元
[max_energy, max_range_idx] = max(center_energy_per_range);
fprintf('最强中心频率能量在距离单元 %d，能量值: %.2e\n', max_range_idx, max_energy);

% 显示原始FFT结果
figure('Name', '原始FFT结果分析');
G_fft = 20*log10(abs(raw_fft)./max(abs(raw_fft(:)) + eps));
imagesc(doppler_axis, sim_params.r_axis, G_fft);
caxis([-40,0]);
xlabel('多普勒频率 (Hz)');
ylabel('距离 (米)');
title('原始FFT结果 - 亮线分析');
colorbar;
axis xy;
colormap('jet');
hold on;
% 标记零多普勒线
plot([0, 0], [min(sim_params.r_axis), max(sim_params.r_axis)], 'r--', 'LineWidth', 2);
legend('零多普勒线', 'Location', 'best');

% 显示中心频率能量分布
figure('Name', '中心频率能量分布');
plot(sim_params.r_axis, 10*log10(center_energy_per_range + eps));
xlabel('距离 (米)');
ylabel('中心频率能量 (dB)');
title('各距离单元的中心频率能量分布');
grid on;

% 分析最强距离单元的频谱
figure('Name', '最强距离单元频谱分析');
subplot(2,1,1);
plot(doppler_axis, abs(raw_fft(max_range_idx, :)));
xlabel('多普勒频率 (Hz)');
ylabel('幅度');
title(sprintf('距离单元 %d 的频谱幅度', max_range_idx));
grid on;

subplot(2,1,2);
plot(doppler_axis, 20*log10(abs(raw_fft(max_range_idx, :)) + eps));
xlabel('多普勒频率 (Hz)');
ylabel('幅度 (dB)');
title(sprintf('距离单元 %d 的频谱幅度 (dB)', max_range_idx));
grid on;

% 分析时域信号特性
fprintf('\n=== 时域信号分析 ===\n');
signal_max_range = echo_data_processed(max_range_idx, :);
signal_mean = mean(abs(signal_max_range));
signal_std = std(abs(signal_max_range));
signal_max = max(abs(signal_max_range));

fprintf('最强距离单元时域信号统计:\n');
fprintf('  均值: %.4e\n', signal_mean);
fprintf('  标准差: %.4e\n', signal_std);
fprintf('  最大值: %.4e\n', signal_max);
fprintf('  变异系数: %.4f\n', signal_std/signal_mean);

% 检查是否存在常数分量或低频趋势
signal_detrend = detrend(real(signal_max_range));
trend_energy = sum((real(signal_max_range) - signal_detrend).^2);
signal_energy = sum(real(signal_max_range).^2);
trend_ratio = trend_energy / signal_energy;

fprintf('  线性趋势能量比例: %.4f (%.2f%%)\n', trend_ratio, trend_ratio*100);

fprintf('分析完成。\n');
