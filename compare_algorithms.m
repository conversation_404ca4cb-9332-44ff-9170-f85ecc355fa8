% compare_algorithms.m
% 对比原始算法和修改后算法的效果

clear; close all; clc;
fprintf('对比原始算法和修改后算法的效果...\n');

% 加载数据
try
    load shipx2.mat; 
    load s_r_tm2.mat
    echo_data = s_r_tm2;
    fprintf('数据加载成功。\n');
catch
    fprintf('数据加载失败，退出对比。\n');
    return;
end

% 设置参数
sim_params = struct();
sim_params.Num_r = size(echo_data, 1);
sim_params.Num_tm = size(echo_data, 2);
sim_params.PRF = 1400;
sim_params.fc = 5.2e9;
sim_params.c = 3e8;
sim_params.B = 80e6;
delta_r_res_actual = sim_params.c / (2*sim_params.B);
r_center_actual = 0;
sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, ...
                             r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);
sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);

doppler_axis = linspace(-sim_params.PRF/2, sim_params.PRF/2, sim_params.Num_tm);

% 1. 原始算法（直接FFT）
fprintf('运行原始算法（直接FFT）...\n');
echo_data_original = echo_data;
for r_bin = 1:size(echo_data_original, 1)
    echo_data_original(r_bin, :) = echo_data_original(r_bin, :) - mean(echo_data_original(r_bin, :));
end
raw_fft_original = fftshift(fft(echo_data_original, [], 2), 2);

% 2. 修改后算法（零多普勒抑制）
fprintf('运行修改后算法（零多普勒抑制）...\n');
echo_data_modified = echo_data;

% 数据预处理（去均值）
for r_bin = 1:size(echo_data_modified, 1)
    echo_data_modified(r_bin, :) = echo_data_modified(r_bin, :) - mean(echo_data_modified(r_bin, :));
end

% 零多普勒抑制
zero_doppler_suppression = true;
center_suppression_threshold = 0.001; % 低阈值
center_suppression_factor = 0.05; % 强抑制
notch_width = 6; % 陷波宽度

if zero_doppler_suppression
    suppression_count = 0;
    
    for r_bin = 1:size(echo_data_modified, 1)
        signal_fft = fft(echo_data_modified(r_bin, :));
        signal_fft_abs = abs(signal_fft);
        
        N = length(signal_fft);
        center_idx = round(N/2) + 1;
        
        center_range = max(1, center_idx-notch_width):min(N, center_idx+notch_width);
        center_energy = sum(signal_fft_abs(center_range).^2);
        total_energy = sum(signal_fft_abs.^2);
        
        if center_energy > center_suppression_threshold * total_energy
            suppression_count = suppression_count + 1;
            signal_fft(center_range) = signal_fft(center_range) * center_suppression_factor;
            echo_data_modified(r_bin, :) = ifft(signal_fft);
        end
    end
    fprintf('对 %d 个距离单元应用了零多普勒抑制\n', suppression_count);
end

% 应用窗函数并FFT
azimuth_window = hamming(sim_params.Num_tm)';
ISAR_image_modified = zeros(size(echo_data_modified), 'like', 1j*echo_data_modified(1));

for r_idx = 1:size(echo_data_modified, 1)
    signal = echo_data_modified(r_idx, :);
    signal = signal - mean(signal);
    ISAR_image_modified(r_idx, :) = fft(signal .* azimuth_window);
end

ISAR_image_modified_shifted = fftshift(ISAR_image_modified, 2);

% 3. 分析对比结果
fprintf('\n=== 对比分析结果 ===\n');

% 计算中心频率能量比例
center_idx = round(sim_params.Num_tm / 2);
center_range = max(1, center_idx-5):min(sim_params.Num_tm, center_idx+5);

% 原始算法
center_energy_original = sum(abs(raw_fft_original(:, center_range)).^2, 'all');
total_energy_original = sum(abs(raw_fft_original(:)).^2, 'all');
center_ratio_original = center_energy_original / total_energy_original;

% 修改后算法
center_energy_modified = sum(abs(ISAR_image_modified_shifted(:, center_range)).^2, 'all');
total_energy_modified = sum(abs(ISAR_image_modified_shifted(:)).^2, 'all');
center_ratio_modified = center_energy_modified / total_energy_modified;

fprintf('原始算法中心频率能量比例: %.4f (%.2f%%)\n', center_ratio_original, center_ratio_original*100);
fprintf('修改后算法中心频率能量比例: %.4f (%.2f%%)\n', center_ratio_modified, center_ratio_modified*100);
fprintf('亮线抑制效果: %.2f%%\n', (1-center_ratio_modified/center_ratio_original)*100);

% 计算图像质量指标
contrast_original = std(abs(raw_fft_original(:))) / mean(abs(raw_fft_original(:)));
contrast_modified = std(abs(ISAR_image_modified_shifted(:))) / mean(abs(ISAR_image_modified_shifted(:)));

fprintf('\n原始算法图像对比度: %.4f\n', contrast_original);
fprintf('修改后算法图像对比度: %.4f\n', contrast_modified);
fprintf('对比度变化: %.2f%%\n', (contrast_modified/contrast_original-1)*100);

% 分析散射点能量分布
fprintf('\n=== 散射点能量分析 ===\n');

% 找到非中心频率的最强散射点
raw_fft_original_no_center = raw_fft_original;
raw_fft_original_no_center(:, center_range) = 0;
[max_val_orig, max_idx_orig] = max(abs(raw_fft_original_no_center(:)));

ISAR_image_modified_no_center = ISAR_image_modified_shifted;
ISAR_image_modified_no_center(:, center_range) = 0;
[max_val_mod, max_idx_mod] = max(abs(ISAR_image_modified_no_center(:)));

fprintf('原始算法最强散射点能量: %.2e\n', max_val_orig);
fprintf('修改后算法最强散射点能量: %.2e\n', max_val_mod);
fprintf('散射点能量变化: %.2f%%\n', (max_val_mod/max_val_orig-1)*100);

% 保存对比结果
save('algorithm_comparison.mat', 'raw_fft_original', 'ISAR_image_modified_shifted', ...
     'center_ratio_original', 'center_ratio_modified', 'contrast_original', 'contrast_modified', ...
     'sim_params', 'doppler_axis');

fprintf('\n对比结果已保存到 algorithm_comparison.mat\n');
fprintf('对比分析完成。\n');
