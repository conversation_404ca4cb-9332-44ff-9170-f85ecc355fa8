% final_comparison.m
% 最终对比原始算法和修改后算法的效果

clear; close all; clc;
fprintf('最终对比分析...\n');

% 1. 运行原始版本的chuanliantestgemi算法
fprintf('运行原始算法 (chuanliantestgemi)...\n');
try
    run('chuanliantestgemi.m');
    ISAR_original = ISAR_image_sparse_shifted;
    fprintf('原始算法运行完成。\n');
catch ME
    fprintf('原始算法运行失败: %s\n', ME.message);
    return;
end

% 2. 加载修改后的结果
fprintf('加载修改后的算法结果...\n');
try
    load('ISAR_results_modified.mat');
    ISAR_modified = fftshift(ISAR_image_fused, 2);
    fprintf('修改后算法结果加载成功。\n');
catch
    fprintf('无法加载修改后的结果。\n');
    return;
end

% 3. 设置显示参数
sim_params = struct();
sim_params.Num_r = size(ISAR_original, 1);
sim_params.Num_tm = size(ISAR_original, 2);
sim_params.PRF = 1400;
sim_params.fc = 5.2e9;
sim_params.c = 3e8;
sim_params.B = 80e6;
delta_r_res_actual = sim_params.c / (2*sim_params.B);
r_center_actual = 0;
sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, ...
                             r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);

doppler_axis = linspace(-sim_params.PRF/2, sim_params.PRF/2, sim_params.Num_tm);

% 4. 分析对比结果
fprintf('\n=== 最终对比分析 ===\n');

% 计算中心频率能量比例
center_idx = round(sim_params.Num_tm / 2);
center_range = max(1, center_idx-5):min(sim_params.Num_tm, center_idx+5);

% 原始算法
center_energy_original = sum(abs(ISAR_original(:, center_range)).^2, 'all');
total_energy_original = sum(abs(ISAR_original(:)).^2, 'all');
center_ratio_original = center_energy_original / total_energy_original;

% 修改后算法
center_energy_modified = sum(abs(ISAR_modified(:, center_range)).^2, 'all');
total_energy_modified = sum(abs(ISAR_modified(:)).^2, 'all');
center_ratio_modified = center_energy_modified / total_energy_modified;

fprintf('原始算法 (chuanliantestgemi) 中心频率能量比例: %.4f (%.2f%%)\n', center_ratio_original, center_ratio_original*100);
fprintf('修改后算法 (testgemi) 中心频率能量比例: %.4f (%.2f%%)\n', center_ratio_modified, center_ratio_modified*100);
fprintf('亮线抑制效果: %.2f%%\n', (1-center_ratio_modified/center_ratio_original)*100);

% 找到最强频率bin
energy_per_freq_original = sum(abs(ISAR_original).^2, 1);
energy_per_freq_modified = sum(abs(ISAR_modified).^2, 1);

[max_energy_orig, max_idx_orig] = max(energy_per_freq_original);
[max_energy_mod, max_idx_mod] = max(energy_per_freq_modified);

fprintf('\n原始算法最强频率bin: %d (%.1f Hz)，能量占比: %.2f%%\n', ...
    max_idx_orig, doppler_axis(max_idx_orig), max_energy_orig/total_energy_original*100);
fprintf('修改后算法最强频率bin: %d (%.1f Hz)，能量占比: %.2f%%\n', ...
    max_idx_mod, doppler_axis(max_idx_mod), max_energy_mod/total_energy_modified*100);

% 计算图像质量指标
contrast_original = std(abs(ISAR_original(:))) / mean(abs(ISAR_original(:)));
contrast_modified = std(abs(ISAR_modified(:))) / mean(abs(ISAR_modified(:)));

fprintf('\n原始算法图像对比度: %.4f\n', contrast_original);
fprintf('修改后算法图像对比度: %.4f\n', contrast_modified);
fprintf('对比度变化: %.2f%%\n', (contrast_modified/contrast_original-1)*100);

% 分析散射点保持情况
fprintf('\n=== 散射点保持分析 ===\n');

% 排除中心频率，找到最强的散射点
ISAR_original_no_center = ISAR_original;
ISAR_original_no_center(:, center_range) = 0;
[max_scatter_orig, ~] = max(abs(ISAR_original_no_center(:)));

ISAR_modified_no_center = ISAR_modified;
ISAR_modified_no_center(:, center_range) = 0;
[max_scatter_mod, ~] = max(abs(ISAR_modified_no_center(:)));

fprintf('原始算法最强散射点能量: %.2e\n', max_scatter_orig);
fprintf('修改后算法最强散射点能量: %.2e\n', max_scatter_mod);
fprintf('散射点能量保持率: %.2f%%\n', max_scatter_mod/max_scatter_orig*100);

% 计算有效散射点数量（能量>最大值的1%）
threshold_orig = max_scatter_orig * 0.01;
threshold_mod = max_scatter_mod * 0.01;

effective_points_orig = sum(abs(ISAR_original_no_center(:)) > threshold_orig);
effective_points_mod = sum(abs(ISAR_modified_no_center(:)) > threshold_mod);

fprintf('原始算法有效散射点数: %d\n', effective_points_orig);
fprintf('修改后算法有效散射点数: %d\n', effective_points_mod);
fprintf('散射点保持率: %.2f%%\n', effective_points_mod/effective_points_orig*100);

% 保存最终对比结果
final_results = struct();
final_results.ISAR_original = ISAR_original;
final_results.ISAR_modified = ISAR_modified;
final_results.center_ratio_original = center_ratio_original;
final_results.center_ratio_modified = center_ratio_modified;
final_results.contrast_original = contrast_original;
final_results.contrast_modified = contrast_modified;
final_results.max_scatter_orig = max_scatter_orig;
final_results.max_scatter_mod = max_scatter_mod;
final_results.effective_points_orig = effective_points_orig;
final_results.effective_points_mod = effective_points_mod;
final_results.doppler_axis = doppler_axis;
final_results.sim_params = sim_params;

save('final_comparison_results.mat', 'final_results');

fprintf('\n=== 总结 ===\n');
fprintf('✓ 亮线抑制效果: %.1f%%\n', (1-center_ratio_modified/center_ratio_original)*100);
fprintf('✓ 散射点能量保持: %.1f%%\n', max_scatter_mod/max_scatter_orig*100);
fprintf('✓ 有效散射点保持: %.1f%%\n', effective_points_mod/effective_points_orig*100);

if (1-center_ratio_modified/center_ratio_original) > 0.3  % 30%以上的抑制效果
    fprintf('✓ 亮线抑制成功！\n');
else
    fprintf('⚠ 亮线抑制效果有限。\n');
end

if max_scatter_mod/max_scatter_orig > 0.7  % 保持70%以上的散射点能量
    fprintf('✓ 散射点信息保持良好！\n');
else
    fprintf('⚠ 散射点信息损失较大。\n');
end

fprintf('\n最终对比结果已保存到 final_comparison_results.mat\n');
fprintf('对比分析完成。\n');
