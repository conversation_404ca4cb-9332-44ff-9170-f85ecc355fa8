% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% ISAR_Main_Script_Enhanced_ADMM_VMD_DCFT_Revised.m
% 主脚本，用于执行增强型深度融合VMD-ADMM-DCFT ISAR成像算法
% (针对用户问题进行了初步修改和参数调整建议)
% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% --------------- 1. 清理环境和加载/生成数据 --------------- %
clearvars; close all; clc;
fprintf('开始ISAR成像处理...\n');
load s_r_tm2.mat
% 仿真参数和回波生成 (假设已存在或通过函数生成)
% [echo_data, sim_params] = generate_simulated_echo_with_preprocessing();
% % 示例：如果直接加载数据
% load('simulated_echo_data.mat'); % 假设 echo_data 和 sim_params 在此文件中

% --- 模拟运行所需，实际使用时请替换为您的数据加载 ---
if ~exist('s_r_tm2', 'var') || ~exist('sim_params', 'var')
    fprintf('未找到回波数据和仿真参数，将生成模拟数据...\n');
    [echo_data, sim_params] = generate_simulated_echo(); % 使用您提供的仿真函数

    % 对仿真数据进行预处理：距离压缩后的回波一般是复数，且可能需要初步的运动补偿或对齐
    % 这里假设 echo_data 已经是距离压缩后、初步处理（如提取感兴趣区域）的数据
    % 确保 echo_data 是复数矩阵 [num_range_bins, num_azimuth_samples]
    % 您的仿真函数 generate_simulated_echo 似乎已经处理了距离压缩

    % 预处理步骤：对每个距离单元进行去均值 (通常在成像算法内部处理，但这里先做一次确保)
    % 这一步在 perform_isar_imaging_fused_admm_enhanced 内部也会做，但可以先做
    for r_bin = 1:size(echo_data, 1)
        echo_data(r_bin, :) = echo_data(r_bin, :) - mean(echo_data(r_bin, :));
    end
    fprintf('模拟数据生成完毕。\n');
end
% --- 模拟运行所需结束 ---


% -------------------- 2. 设置处理参数 -------------------- %
params_proc = struct();

% VMD 参数
params_proc.vmd.K = 3; % 模态数量 (可根据目标复杂性调整)
params_proc.vmd.alpha_vmd = 2000; % VMD带宽限制参数
params_proc.vmd.tau_vmd = 0; % VMD噪声容限 (通常为0)
params_proc.vmd.tol_vmd_inner = 1e-6; % VMD内部迭代收敛容限 (可适当提高精度)
params_proc.vmd.max_iter_vmd_inner = 50; % VMD内部最大迭代次数 (可适当增加)
params_proc.vmd.init_omega_method = 'peaks_robust'; % VMD初始中心频率估计算法 ('peaks', 'linear', 'peaks_robust')
params_proc.vmd.alpha_phase_guidance = 0.2; % VMD相位引导权重 (可调整, 0.1-0.5)

% 相位估计参数 (DCFT部分)
params_proc.phase_est.poly_order = 3; % 相位多项式阶数 (3阶适用于复杂运动)
params_proc.phase_est.fd_search_range_factor = 0.5; % 多普勒中心频率搜索范围因子
params_proc.phase_est.ka_search_pts = 31; % 调频率搜索点数 (可增加以提高精度)
params_proc.phase_est.kb_search_pts = 31; % 二阶调频率搜索点数 (可增加)
params_proc.phase_est.sharpness_weight = 0.02; % 图像锐度权重 (关键参数, 需仔细调整, e.g., 0.01-0.1)
params_proc.phase_est.num_refinement_passes = 3; % 相位系数迭代优化次数 (可增加)

% ADMM 全局参数
params_proc.admm_global.rho_X = 1.0; % ADMM罚参数 (X谱)
params_proc.admm_global.rho_U = 0.5; % ADMM罚参数 (U模态)
params_proc.admm_global.lambda_sparsity = 0.03; % 稀疏权重 (可调整, e.g., 0.01-0.05)
% %%%%%%%%%%%%%%%%%%%%%%%% 主要修改点 %%%%%%%%%%%%%%%%%%%%%%%%%%%%
params_proc.admm_global.max_iter = 100; % ADMM最大迭代次数 (显著增加，原为2，严重不足)
params_proc.admm_global.tol = 1e-5; % ADMM收敛容限 (原为1e-4, 可配合迭代次数调整)
% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% 其他处理参数
params_proc.apply_azimuth_window = true; % 是否在最终FFT前应用窗函数 (推荐Hamming或Taylor)
params_proc.num_azimuth = sim_params.Num_tm;
params_proc.num_range_bins = sim_params.Num_r;
params_proc.PRF = sim_params.PRF;
params_proc.fc = sim_params.fc;
params_proc.c = sim_params.c;
params_proc.tm_azimuth = sim_params.tm;
params_proc.normalized_tm = (0:params_proc.num_azimuth-1) / params_proc.num_azimuth; % 归一化慢时间


% -------------------- 3. 执行增强型深度融合ISAR成像算法 -------------------- %
fprintf('开始执行增强型深度融合VMD-ADMM-DCFT ISAR成像算法 (已修改参数)...\n');
tic;
[ISAR_image_fused, dominant_mode_compensated_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = ...
    perform_isar_imaging_fused_admm_enhanced(echo_data, params_proc, sim_params);
fprintf('增强型深度融合ISAR成像处理完毕。耗时: %.2f 秒\n', toc);

% -------------------- 4. 显示结果 -------------------- %
fprintf('显示成像结果...\n');

% 原始数据和直接FFT
figure('Name', '原始数据和直接FFT');
subplot(1,2,1);
imagesc(sim_params.tm, sim_params.r_axis, abs(echo_data));
xlabel('慢时间 (秒)'); ylabel('距离 (米)'); title('距离压缩后的原始回波 (已预处理)'); colorbar; axis xy;

% 对直接FFT也应用预处理（如果原始数据被修改了）
echo_data_for_fft_display = echo_data; % radar_data 是 perform_isar_imaging_fused_admm_enhanced 的输入
% 在 perform_isar_imaging_fused_admm_enhanced 内部已经对每个距离单元的 signal 做了去均值
% 为保持一致性，这里的直接FFT显示也应该基于去均值数据
for r_bin = 1:size(echo_data_for_fft_display, 1)
    echo_data_for_fft_display(r_bin, :) = echo_data_for_fft_display(r_bin, :) - mean(echo_data_for_fft_display(r_bin, :));
end
% 应用窗函数 (如果成像算法中也用了)
azimuth_window_display = ones(1, params_proc.num_azimuth);
if params_proc.apply_azimuth_window
    azimuth_window_display = hamming(params_proc.num_azimuth)';
end
echo_data_windowed_display = echo_data_for_fft_display .* repmat(azimuth_window_display, params_proc.num_range_bins, 1);
raw_fft = fftshift(fft(echo_data_windowed_display, [], 2), 2);

doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, params_proc.num_azimuth);
subplot(1,2,2);
imagesc(doppler_axis, sim_params.r_axis, abs(raw_fft));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('原始数据直接FFT (预处理, 加窗后)'); colorbar; axis xy;

% 增强型融合结果
ISAR_image_fused_shifted = fftshift(ISAR_image_fused, 2); % 确保最终结果也做fftshift
figure('Name', '增强型深度融合ADMM成像结果 (已修改参数)');
imagesc(doppler_axis, sim_params.r_axis, abs(ISAR_image_fused_shifted));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('增强型VMD-ADMM-DCFT ISAR结果 (已修改参数)'); colorbar; axis xy;

% 对数尺度对比
figure('Name', '对数尺度对比 - 直接FFT (已修改参数)');
G_raw = 20*log10(abs(raw_fft)./max(abs(raw_fft(:)) + eps));
imagesc(doppler_axis, sim_params.r_axis, G_raw); caxis([-40,0]); % 调整动态范围
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('直接FFT (dB, 预处理, 加窗后)'); colorbar; axis xy; colormap('jet');

figure('Name', '对数尺度对比 - 增强型融合 (已修改参数)');
G_fused = 20*log10(abs(ISAR_image_fused_shifted)./max(abs(ISAR_image_fused_shifted(:)) + eps));
imagesc(doppler_axis, sim_params.r_axis, G_fused); caxis([-35,0]); % 调整动态范围
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('增强型融合ADMM (dB, 已修改参数)'); colorbar; axis xy; colormap('jet');

% 可以增加显示ADMM收敛曲线的代码
% Example: Plotting convergence for a middle range bin
% mid_r_idx = round(params_proc.num_range_bins/2);
% if ~isempty(admm_convergence_all_bins) && mid_r_idx <= length(admm_convergence_all_bins) && isstruct(admm_convergence_all_bins{mid_r_idx})
%     conv_data = admm_convergence_all_bins{mid_r_idx};
%     if isfield(conv_data, 'primal_res_X') && ~isempty(conv_data.primal_res_X)
%         figure('Name', ['ADMM 收敛曲线 (距离单元 ' num2str(mid_r_idx) ')']);
%         iters = 1:length(conv_data.primal_res_X);
%         semilogy(iters, conv_data.primal_res_X, '-o', 'DisplayName', 'Primal Res X'); hold on;
%         semilogy(iters, conv_data.dual_res_X, '-o', 'DisplayName', 'Dual Res X');
%         semilogy(iters, conv_data.primal_res_U, '-s', 'DisplayName', 'Primal Res U');
%         semilogy(iters, conv_data.dual_res_U, '-s', 'DisplayName', 'Dual Res U');
%         hold off;
%         xlabel('ADMM 迭代次数'); ylabel('相对残差'); title(['ADMM 收敛性 (距离单元 ' num2str(mid_r_idx) ')']);
%         legend show; grid on;
%     end
% end


% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%                       核心成像算法函数 (部分修改和注释)
% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function [ISAR_image_sparse, s_compensated_dominant_mode_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = ...
    perform_isar_imaging_fused_admm_enhanced(radar_data, params_proc, sim_params)
    % radar_data: 距离压缩后的回波数据 (num_range_bins x num_azimuth_samples)
    % params_proc: 处理参数结构体
    % sim_params: 仿真参数结构体 (用于获取PRF, fc等)

    [num_range_bins, num_azimuth] = size(radar_data);
    fprintf('  处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

    % 提取参数
    K_vmd = params_proc.vmd.K;
    poly_order_phase = params_proc.phase_est.poly_order;
    tm_normalized = params_proc.normalized_tm; % 归一化慢时间轴 [0, 1)

    rho_X = params_proc.admm_global.rho_X;
    rho_U = params_proc.admm_global.rho_U;
    lambda_sparsity = params_proc.admm_global.lambda_sparsity;
    max_admm_iter = params_proc.admm_global.max_iter; % 使用修改后的迭代次数
    admm_tol = params_proc.admm_global.tol;         % 使用修改后的容限

    sharpness_weight = params_proc.phase_est.sharpness_weight;
    num_phase_refinement_passes = params_proc.phase_est.num_refinement_passes;
    apply_azimuth_window_flag = params_proc.apply_azimuth_window;

    % 初始化输出
    ISAR_image_sparse = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
    s_compensated_dominant_mode_fft = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1)); % 用于对比或特定分析

    vmd_modes_all_bins = cell(num_range_bins, 1);
    phase_coeffs_all_bins = cell(num_range_bins, 1);
    admm_convergence_all_bins = cell(num_range_bins, 1); % 存储每个距离单元的ADMM收敛信息

    % 方位窗函数 (若使用)
    azimuth_window = ones(1, num_azimuth);
    if apply_azimuth_window_flag
        azimuth_window = hamming(num_azimuth)'; % Hamming窗, 可替换为Taylor等
        % azimuth_window = taylorwin(num_azimuth, 5, -35)'; % Taylor窗示例
    end

    fprintf('  开始逐距离单元处理 (共 %d 个)...\n', num_range_bins);
    
    % 使用 parfor 进行并行处理以加速 (如果您的MATLAB版本和工具箱支持)
    % 如果不使用并行，请将 parfor 改为 for
    parfor r_idx = 1:num_range_bins
    % for r_idx = 1:num_range_bins % 非并行版本
        if mod(r_idx, 20) == 0 % 每处理20个单元打印一次进度
            fprintf('    正在处理距离单元 %d / %d ...\n', r_idx, num_range_bins);
        end
        
        signal_orig_for_range_bin = radar_data(r_idx, :);

        % 能量检测，跳过低能量距离单元 (阈值可能需要根据实际数据调整)
        if sum(abs(signal_orig_for_range_bin).^2) < 1e-10 * num_azimuth % 调整能量阈值
            % 对于低能量单元，直接进行FFT或置零
            ISAR_image_sparse(r_idx, :) = fft(signal_orig_for_range_bin .* azimuth_window); % 保持与后续处理一致
            s_compensated_dominant_mode_fft(r_idx, :) = fft(signal_orig_for_range_bin .* azimuth_window);
            
            % 初始化空的收敛数据
            admm_iter_data_empty = struct('primal_res_X', NaN, 'dual_res_X', NaN, 'primal_res_U', NaN, 'dual_res_U', NaN, 'iters_run', 0);
            admm_convergence_all_bins{r_idx} = admm_iter_data_empty;
            vmd_modes_all_bins{r_idx} = zeros(K_vmd, num_azimuth, 'like', 1j*signal_orig_for_range_bin(1));
            phase_coeffs_all_bins{r_idx} = zeros(K_vmd, poly_order_phase + 1); % 多项式系数是 poly_order+1 个
            continue;
        end

        % 信号归一化和去均值
        signal_norm_factor = max(abs(signal_orig_for_range_bin));
        if signal_norm_factor < eps, signal_norm_factor = 1; end
        signal = signal_orig_for_range_bin / signal_norm_factor;
        signal = signal - mean(signal); % 确保VMD输入为零均值

        % 初始化VMD模态和中心频率
        u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));
        omega_k_init = zeros(K_vmd, 1); % VMD初始中心频率 (归一化)
        
        % VMD 初始化 (Robust VMD initialization)
        fft_signal_abs = abs(fft(signal));
        valid_omega_idx = 0; % 用于跟踪成功找到的 omega 个数
        if strcmp(params_proc.vmd.init_omega_method, 'peaks_robust')
            [pks, locs] = findpeaks(fft_signal_abs(1:floor(num_azimuth/2)), 'SortStr', 'descend'); % 只在正频率找，避免镜像
            temp_omega_k = zeros(K_vmd,1);
            min_freq_sep_norm = 0.05; % 最小归一化频率间隔 (可调)
            last_added_omega_norm = -inf;
            pk_count = 0;
            for i_pk = 1:length(locs)
                if pk_count == K_vmd, break; end
                current_omega_norm = (locs(i_pk)-1)/num_azimuth;
                % 避免直流分量 (除非K=1) 且保证频率间隔
                is_dc_like = abs(current_omega_norm) < 0.01 || abs(current_omega_norm - 0.5) < 0.01; % 考虑归一化频率的直流和奈奎斯特附近
                 if ~(K_vmd > 1 && is_dc_like)
                    if abs(current_omega_norm - last_added_omega_norm) > min_freq_sep_norm || pk_count == 0
                        pk_count = pk_count + 1;
                        temp_omega_k(pk_count) = current_omega_norm;
                        last_added_omega_norm = current_omega_norm;
                    end
                end
            end
            valid_omega_idx = pk_count;
            omega_k_init(1:valid_omega_idx) = temp_omega_k(1:valid_omega_idx);

        elseif strcmp(params_proc.vmd.init_omega_method, 'peaks')
            [~, locs] = findpeaks(fft_signal_abs(1:floor(num_azimuth/2)), 'SortStr', 'descend', 'NPeaks', K_vmd);
            if ~isempty(locs)
                valid_locs_count = min(length(locs), K_vmd);
                omega_k_init(1:valid_locs_count) = (locs(1:valid_locs_count)-1)/num_azimuth;
                valid_omega_idx = valid_locs_count;
            end
        else % 'linear'
            for k_idx_init = 1:K_vmd
                omega_k_init(k_idx_init) = (k_idx_init-1)/(K_vmd); % 线性间隔
            end
            valid_omega_idx = K_vmd;
        end
        
        % 如果找到的峰值不够K_vmd个，用线性间隔填充剩余的
        if valid_omega_idx < K_vmd
            remaining_omegas = K_vmd - valid_omega_idx;
            current_max_omega = 0;
            if valid_omega_idx > 0, current_max_omega = max(omega_k_init(1:valid_omega_idx)); end
            
            % 尝试在未被占用的频率范围内均匀分布
            fill_omegas = linspace(current_max_omega + min_freq_sep_norm, 0.5 - min_freq_sep_norm, remaining_omegas + 2); 
            fill_omegas = fill_omegas(2:end-1); % 去掉两端
            if length(fill_omegas) > remaining_omegas, fill_omegas = fill_omegas(1:remaining_omegas); end
            
            assigned_count = 0;
            for i_rem = 1:length(fill_omegas)
                if valid_omega_idx + assigned_count < K_vmd
                    omega_k_init(valid_omega_idx + assigned_count + 1) = fill_omegas(i_rem);
                    assigned_count = assigned_count + 1;
                end
            end
             valid_omega_idx = valid_omega_idx + assigned_count;
             % 如果仍然不够，则强制线性填充
             if valid_omega_idx < K_vmd
                 for k_fill = valid_omega_idx+1:K_vmd
                     omega_k_init(k_fill) = (k_fill-1)/(2*K_vmd) + 0.25; % 避免0和0.5
                 end
             end
        end
        omega_k = omega_k_init; % 将初始化后的 omega_k 用于迭代

        % 初始化相位多项式系数 (poly_order+1 个系数，包括常数项，但通常常数项不估计或设为0)
        % 这里 poly_coeffs_k 存储的是 fd, ka, kb 等归一化参数
        poly_coeffs_k = zeros(K_vmd, poly_order_phase); % (fd, ka, kb for poly_order=3)
        estimated_phases_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));

        % 基于初始 omega_k 初始化多项式系数中的 fd (一次项系数)
        if poly_order_phase >= 1
            for k_init_phase = 1:K_vmd
                fd_norm_init = omega_k(k_init_phase); % omega_k 是 [0,1)
                % 转换为 [-0.5, 0.5) 以匹配多普勒频率的常规表示
                if fd_norm_init > 0.5, fd_norm_init = fd_norm_init - 1; end
                poly_coeffs_k(k_init_phase, 1) = fd_norm_init;
                estimated_phases_k(k_init_phase, :) = construct_phase_poly(tm_normalized, poly_coeffs_k(k_init_phase,:), params_proc.PRF); % 传递PRF
            end
        end
        
        % 初始化ADMM变量
        X_sparse_spectrum = fft(signal .* azimuth_window); % 初始稀疏谱 (加窗后FFT)
        Z_aux_X = X_sparse_spectrum; % 辅助变量
        Y_lagrange_X = zeros(size(X_sparse_spectrum), 'like', 1j*signal(1)); % 拉格朗日乘子 for X
        Y_lagrange_U = zeros(size(signal), 'like', 1j*signal(1)); % 拉格朗日乘子 for U (modes sum)
                                                               
        admm_iter_data = struct('primal_res_X', zeros(1,max_admm_iter), 'dual_res_X', zeros(1,max_admm_iter), ...
                                'primal_res_U', zeros(1,max_admm_iter), 'dual_res_U', zeros(1,max_admm_iter), 'iters_run', 0);
        
        u_k_prev_for_dual_U = u_k; % 用于计算U的对偶残差
        
        % ADMM 主循环
        for iter_admm = 1:max_admm_iter
            X_prev_for_dual_X = X_sparse_spectrum;
            Z_aux_X_prev_for_dual_X = Z_aux_X;
            
            % 1. 更新 VMD 模态 u_k 和中心频率 omega_k
            target_signal_for_vmd = signal + Y_lagrange_U / rho_U;
            target_signal_for_vmd = target_signal_for_vmd - mean(target_signal_for_vmd); % 确保VMD输入零均值
            current_phase_models_for_vmd = estimated_phases_k; % 使用上一轮ADMM迭代的相位模型
            [u_k, omega_k] = update_modes_admm(target_signal_for_vmd, u_k, omega_k, current_phase_models_for_vmd, params_proc, rho_U);
            
            % 2. 更新相位多项式系数 poly_coeffs_k 和相位 estimated_phases_k
            S_reconstructed_from_modes_new = sum(u_k, 1); % 当前模态叠加，用于U的原始残差
            X_target_global = X_sparse_spectrum + Y_lagrange_X / rho_X; % X子问题的目标

            % 计算除当前更新模态外，其他模态补偿后的频谱和
            temp_sum_compensated_others_fft = zeros(1, num_azimuth, 'like', 1j*signal(1));
            for k_other = 1:K_vmd
                % 使用当前迭代的 u_k 和上一迭代的 estimated_phases_k (或本轮迭代开始时的)
                uk_other_processed = u_k(k_other,:) - mean(u_k(k_other,:));
                temp_sum_compensated_others_fft = temp_sum_compensated_others_fft + ...
                    fft( uk_other_processed .* exp(-1j * estimated_phases_k(k_other,:)) .* azimuth_window );
            end
            
            new_poly_coeffs_k = poly_coeffs_k; % 存储本轮ADMM迭代更新后的相位系数
            new_estimated_phases_k = estimated_phases_k;

            for k_update_phase = 1:K_vmd
                uk_current_mode_processed = u_k(k_update_phase,:) - mean(u_k(k_update_phase,:));
                % 从总和中减去旧的当前模态贡献
                current_uk_compensated_fft_old_phase = fft(uk_current_mode_processed .* exp(-1j * estimated_phases_k(k_update_phase,:)) .* azimuth_window);
                Residual_Target_Spectrum_k = X_target_global - (temp_sum_compensated_others_fft - current_uk_compensated_fft_old_phase);
                
                [coeffs_k, phase_val_k] = update_phase_coeffs_admm_enhanced(...
                    uk_current_mode_processed, ...
                    poly_coeffs_k(k_update_phase,:), ... % 上一轮的系数作为初值
                    Residual_Target_Spectrum_k, ...
                    params_proc, tm_normalized, sharpness_weight, azimuth_window);
                
                new_poly_coeffs_k(k_update_phase,:) = coeffs_k;
                new_estimated_phases_k(k_update_phase,:) = phase_val_k;
                
                % 更新 temp_sum_compensated_others_fft 以反映当前模态相位系数的更新
                temp_sum_compensated_others_fft = temp_sum_compensated_others_fft - current_uk_compensated_fft_old_phase ...
                                                + fft(uk_current_mode_processed .* exp(-1j*phase_val_k) .* azimuth_window);
            end
            poly_coeffs_k = new_poly_coeffs_k;
            estimated_phases_k = new_estimated_phases_k;
            
            % 3. 更新稀疏谱 X_sparse_spectrum (X-minimization)
            %   首先构造所有模态用更新后的相位补偿并叠加后的时域信号
            s_compensated_time = zeros(1, num_azimuth, 'like', signal(1));
            for k_idx = 1:K_vmd
                s_compensated_time = s_compensated_time + (u_k(k_idx,:) - mean(u_k(k_idx,:))) .* exp(-1j * estimated_phases_k(k_idx,:));
            end
            s_compensated_time = s_compensated_time - mean(s_compensated_time); % 再次去均值
            S_compensated_fft = fft(s_compensated_time .* azimuth_window); % 加窗后FFT
            
            % X 更新公式: (S_compensated_fft + rho_X * (Z_aux_X - Y_lagrange_X/rho_X)) / (1 + rho_X)
            % 注意：文献中可能有不同形式，这里假设 X 是最终的稀疏谱
            X_sparse_spectrum = (S_compensated_fft + rho_X * Z_aux_X - Y_lagrange_X) / (1 + rho_X); % 修正 Y_lagrange_X 的符号使用

            % 4. 更新辅助变量 Z_aux_X (Z-minimization via soft thresholding)
            Z_aux_X = soft_threshold(X_sparse_spectrum + Y_lagrange_X/rho_X, lambda_sparsity/rho_X);
            
            % 5. 更新拉格朗日乘子 Y_lagrange_X, Y_lagrange_U
            Y_lagrange_X = Y_lagrange_X + rho_X * (X_sparse_spectrum - Z_aux_X);
            Y_lagrange_U = Y_lagrange_U + rho_U * (signal - S_reconstructed_from_modes_new); % signal 是原始信号(已去均值)
            
            % 计算残差和收敛判断
            norm_X_prev = norm(X_prev_for_dual_X); if norm_X_prev < eps, norm_X_prev = 1; end
            norm_Y_X_curr = norm(Y_lagrange_X); if norm_Y_X_curr < eps, norm_Y_X_curr = 1; end
            norm_signal_curr = norm(signal); if norm_signal_curr < eps, norm_signal_curr = 1; end
            norm_Y_U_curr = norm(Y_lagrange_U); if norm_Y_U_curr < eps, norm_Y_U_curr = 1; end

            primal_res_X_val = norm(X_sparse_spectrum - Z_aux_X) / (norm(X_sparse_spectrum) + eps); % 原始残差 for X
            dual_res_X_val = rho_X * norm(Z_aux_X - Z_aux_X_prev_for_dual_X) / (norm(Y_lagrange_X) + eps); % 对偶残差 for X
            
            primal_res_U_val = norm(signal - S_reconstructed_from_modes_new) / (norm(signal) + eps); % 原始残差 for U
            dual_res_U_val = rho_U * norm(sum(u_k,1) - sum(u_k_prev_for_dual_U,1)) / (norm(Y_lagrange_U) + eps); % 对偶残差 for U
            
            admm_iter_data.primal_res_X(iter_admm) = primal_res_X_val;
            admm_iter_data.dual_res_X(iter_admm) = dual_res_X_val;
            admm_iter_data.primal_res_U(iter_admm) = primal_res_U_val;
            admm_iter_data.dual_res_U(iter_admm) = dual_res_U_val;
            admm_iter_data.iters_run = iter_admm;

            u_k_prev_for_dual_U = u_k; % 更新用于下次迭代计算对偶残差的 u_k
            
            if iter_admm > 1 % 从第二次迭代开始检查收敛
               if (primal_res_X_val < admm_tol && dual_res_X_val < admm_tol && ...
                   primal_res_U_val < admm_tol && dual_res_U_val < admm_tol)
                    %fprintf('  ADMM converged at iteration %d for range bin %d.\n', iter_admm, r_idx);
                    admm_iter_data.primal_res_X = admm_iter_data.primal_res_X(1:iter_admm);
                    admm_iter_data.dual_res_X = admm_iter_data.dual_res_X(1:iter_admm);
                    admm_iter_data.primal_res_U = admm_iter_data.primal_res_U(1:iter_admm);
                    admm_iter_data.dual_res_U = admm_iter_data.dual_res_U(1:iter_admm);
                    break; % 提前退出 ADMM 循环
               end
            end
        end % ADMM loop
        
        % 存储当前距离单元的结果
        ISAR_image_sparse(r_idx, :) = X_sparse_spectrum * signal_norm_factor; % 恢复能量尺度
        vmd_modes_all_bins{r_idx} = u_k * signal_norm_factor;
        phase_coeffs_all_bins{r_idx} = poly_coeffs_k;
        admm_convergence_all_bins{r_idx} = admm_iter_data;
        
        % 计算主导模态补偿后的FFT (用于分析或对比)
        mode_energies = sum(abs(u_k).^2, 2);
        [~, dominant_idx_candidates] = sort(mode_energies, 'descend');
        if ~isempty(dominant_idx_candidates)
            dominant_idx = dominant_idx_candidates(1);
            % 使用最终估计的该模态的相位系数进行补偿
            dominant_phase_compensation = construct_phase_poly(tm_normalized, poly_coeffs_k(dominant_idx, :), params_proc.PRF);
            
            % 对原始信号(去均值)进行主导模态相位补偿
            s_comp_dom_mode_time = (signal_orig_for_range_bin - mean(signal_orig_for_range_bin)) .* exp(-1j * dominant_phase_compensation);
            s_comp_dom_mode_time = s_comp_dom_mode_time - mean(s_comp_dom_mode_time); % 再次去均值
            s_compensated_dominant_mode_fft(r_idx, :) = fft(s_comp_dom_mode_time .* azimuth_window);
        else
             % 如果没有有效模态，则用原始信号的FFT代替
             s_comp_dom_mode_time_fallback = signal_orig_for_range_bin - mean(signal_orig_for_range_bin);
             s_compensated_dominant_mode_fft(r_idx, :) = fft(s_comp_dom_mode_time_fallback .* azimuth_window);
        end

    end % parfor r_idx (loop over range bins)
    fprintf('  所有距离单元处理完毕。\n');
end

% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%                       辅助函数 (部分修改和注释)
% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function [poly_coeffs_updated_k, estimated_phase_updated_k] = update_phase_coeffs_admm_enhanced(...
    signal_mode_k, poly_coeffs_prev_k, Residual_Target_Spectrum_k, ...
    params_proc, tm_normalized, sharpness_weight, azimuth_window)
    % signal_mode_k: 当前VMD模态 (时域, 已去均值)
    % poly_coeffs_prev_k: 上一轮的相位系数 [fd_norm, ka_norm, kb_norm,...]
    % Residual_Target_Spectrum_k: 补偿目标谱 (频域)
    % params_proc, tm_normalized, sharpness_weight, azimuth_window: 其他参数

    poly_order = params_proc.phase_est.poly_order;
    PRF = params_proc.PRF; % 需要PRF来转换归一化系数到实际物理意义（如果需要）
    N = length(tm_normalized);
    num_refinement_passes = params_proc.phase_est.num_refinement_passes;

    % 如果模态能量过低，则不进行相位估计
    if sum(abs(signal_mode_k).^2) < 1e-12 % 能量阈值
        poly_coeffs_updated_k = zeros(1, poly_order); % 返回零系数
        estimated_phase_updated_k = zeros(1, N);
        return;
    end

    fd_search_range_factor = params_proc.phase_est.fd_search_range_factor; % 归一化频率搜索范围因子
    % 搜索点数现在从 params_proc 中获取
    num_fd_pts = params_proc.phase_est.ka_search_pts; % Note: param name might be confusing, using ka_search_pts for fd
    ka_search_pts_num = params_proc.phase_est.ka_search_pts;
    kb_search_pts_num = params_proc.phase_est.kb_search_pts;
    
    temp_coeffs = poly_coeffs_prev_k; % 从上一轮的系数开始优化
    min_total_cost = inf;

    % 多轮优化，每轮缩小搜索范围 (或者采用更高级的优化策略)
    for pass = 1:num_refinement_passes
        % 1. 优化 fd (多普勒中心频率)
        if poly_order >= 1
            fd_center = temp_coeffs(1);
            % 动态调整搜索范围，随着pass增加而减小
            fd_search_half_range_norm = fd_search_range_factor * 0.5 / (2^(pass-1)); % 归一化频率范围
            fd_search_values_norm = linspace(fd_center - fd_search_half_range_norm, fd_center + fd_search_half_range_norm, num_fd_pts);
            fd_search_values_norm = utilidad_wrap_phase_coeffs(fd_search_values_norm, 0.5); % 确保在 [-0.5, 0.5)

            current_best_fd_cost = min_total_cost; % 初始化为当前已知的最小代价
            if pass == 1, current_best_fd_cost = inf; end % 第一次pass时重新计算
            best_fd_val_pass = temp_coeffs(1);

            for fd_val_norm = fd_search_values_norm
                temp_coeffs(1) = fd_val_norm;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs, PRF);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window);
                
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps);
                match_error_val = 0.5 * norm(Residual_Target_Spectrum_k - compensated_signal_fft)^2;
                cost = -sharpness_weight * sharpness_val + match_error_val;

                if cost < current_best_fd_cost
                    current_best_fd_cost = cost;
                    best_fd_val_pass = fd_val_norm;
                end
            end
            temp_coeffs(1) = best_fd_val_pass;
            min_total_cost = current_best_fd_cost;
        end

        % 2. 优化 ka (线性调频率)
        if poly_order >= 2
            ka_center = temp_coeffs(2);
            % ka_norm_max_abs_heuristic: 归一化调频率的搜索范围启发式值
            % 假设最大多普勒变化为 PRF/2，发生在 T_obs/2 (观测时间一半)
            % ka_actual_max = (PRF/2) / (N/(2*PRF)) = PRF^2/N
            % ka_norm_max = ka_actual_max / PRF^2 = 1/N
            ka_norm_search_half_range = (1/N) * 0.5 / (2^(pass-1)); % 这是一个更基于物理的启发值
            % ka_norm_max_abs_heuristic = (PRF/2)^2 * 0.1 / PRF^2 * 0.5 / (2^(pass-1)); % 原启发值
            ka_search_values_norm = linspace(ka_center - ka_norm_search_half_range, ka_center + ka_norm_search_half_range, ka_search_pts_num);
            
            current_best_ka_cost = min_total_cost;
            best_ka_val_pass = temp_coeffs(2);
            for ka_val_norm = ka_search_values_norm
                temp_coeffs(2) = ka_val_norm;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs, PRF);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window);
                
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps);
                match_error_val = 0.5 * norm(Residual_Target_Spectrum_k - compensated_signal_fft)^2;
                cost = -sharpness_weight * sharpness_val + match_error_val;

                if cost < current_best_ka_cost
                    current_best_ka_cost = cost;
                    best_ka_val_pass = ka_val_norm;
                end
            end
            temp_coeffs(2) = best_ka_val_pass;
            min_total_cost = current_best_ka_cost;
        end

        % 3. 优化 kb (二阶调频率)
        if poly_order >= 3
            kb_center = temp_coeffs(3);
            % kb_norm_max_abs_heuristic: 归一化二阶调频率的搜索范围启发式值
            % kb_actual_max approx (PRF/2) / (T_obs/2)^2 = 2*PRF / (N/PRF)^2 = 2*PRF^3/N^2
            % kb_norm_max = kb_actual_max / PRF^3 = 2/N^2
            kb_norm_search_half_range = (2/(N^2)) * 0.5 / (2^(pass-1));
            % kb_norm_max_abs_heuristic = (PRF/2)^3 / PRF^3 * 0.05 / (2^(pass-1)); % 原启发值
            kb_search_values_norm = linspace(kb_center - kb_norm_search_half_range, kb_center + kb_norm_search_half_range, kb_search_pts_num);

            current_best_kb_cost = min_total_cost;
            best_kb_val_pass = temp_coeffs(3);
            for kb_val_norm = kb_search_values_norm
                temp_coeffs(3) = kb_val_norm;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs, PRF);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window);
                
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps);
                match_error_val = 0.5 * norm(Residual_Target_Spectrum_k - compensated_signal_fft)^2;
                cost = -sharpness_weight * sharpness_val + match_error_val;
                
                if cost < current_best_kb_cost
                    current_best_kb_cost = cost;
                    best_kb_val_pass = kb_val_norm;
                end
            end
            temp_coeffs(3) = best_kb_val_pass;
            min_total_cost = current_best_kb_cost;
        end
    end % End refinement passes
    
    poly_coeffs_updated_k = temp_coeffs;
    estimated_phase_updated_k = construct_phase_poly(tm_normalized, poly_coeffs_updated_k, PRF);
end

function wrapped_coeffs = utilidad_wrap_phase_coeffs(coeffs, max_abs_val)
    % 将系数包装到 [-max_abs_val, max_abs_val)
    % 例如，对于归一化频率 fd_norm，max_abs_val = 0.5
    wrapped_coeffs = mod(coeffs + max_abs_val, 2*max_abs_val) - max_abs_val;
end

function [u_k_updated, omega_k_updated] = update_modes_admm(target_signal_for_vmd, u_k_prev, omega_k_prev, phase_models_k, params_proc, rho_U)
    % target_signal_for_vmd: VMD的输入信号 (时域, 已去均值)
    % u_k_prev, omega_k_prev: 上一轮的模态和中心频率
    % phase_models_k: 当前估计的各模态相位 (用于相位引导)
    % params_proc, rho_U: 其他参数

    alpha_vmd = params_proc.vmd.alpha_vmd;
    K = params_proc.vmd.K;
    tol_vmd_inner = params_proc.vmd.tol_vmd_inner;
    max_iter_vmd_inner = params_proc.vmd.max_iter_vmd_inner;
    alpha_phase_guidance = params_proc.vmd.alpha_phase_guidance; % 相位引导权重
    
    N = length(target_signal_for_vmd);
    target_signal_fft = fft(target_signal_for_vmd);
    f_axis_normalized = params_proc.normalized_tm; % 归一化频率轴 [0, 1) -> FFT的索引对应

    u_k = u_k_prev;
    omega_k = omega_k_prev; % omega_k 是归一化频率 [0,1)
    
    u_k_fft = zeros(K, N, 'like', 1j*target_signal_fft(1));
    for k_idx = 1:K, u_k_fft(k_idx,:) = fft(u_k(k_idx,:)); end % 初始化 u_k_fft
    
    lambda_lagrange_vmd = zeros(1, N, 'like', 1j*target_signal_fft(1)); % VMD内部的拉格朗日乘子 (如果使用严格的VMD形式)
                                                                    % 当前代码似乎未使用此乘子更新，而是直接在ADMM框架下迭代
    
    % VMD 内部迭代 (更新 u_k 和 omega_k)
    for iter_inner = 1:max_iter_vmd_inner
        u_sum_fft_prev_iter_vmd_loop = sum(u_k_fft,1); % 用于VMD内部收敛判断

        for k_idx = 1:K
            % 更新 u_k (频域)
            sum_other_modes_fft = sum(u_k_fft,1) - u_k_fft(k_idx,:);
            
            % VMD原始更新公式中的分子部分 (不含拉格朗日乘子lambda)
            numerator_fft = target_signal_fft - sum_other_modes_fft; % (f - sum_{i!=k} u_i)
            
            % VMD原始更新公式中的分母部分
            % (1 + 2*alpha * (omega - omega_k)^2)
            % f_axis_normalized 对应于 FFT 的频率点 (0 to N-1)/N
            % omega_k(k_idx) 是该模态的中心频率 (0 to 1)
            % 需要处理频率轴的周期性，确保 (f_axis_normalized - omega_k(k_idx)) 计算的是最短距离
            freq_diff = f_axis_normalized - omega_k(k_idx);
            freq_diff(freq_diff > 0.5) = freq_diff(freq_diff > 0.5) - 1;
            freq_diff(freq_diff < -0.5) = freq_diff(freq_diff < -0.5) + 1;
            denominator = 1 + 2*alpha_vmd*(freq_diff).^2;
            
            % 加入ADMM框架下的 rho_U 项 (来自 Y_lagrange_U / rho_U)
            % target_signal_for_vmd = signal + Y_lagrange_U / rho_U
            % 在ADMM中，u_k的更新目标是最小化 ||u_k - (target_signal_for_vmd - sum_{i!=k} u_i)||^2 + VMD正则项
            % 因此，这里的 target_signal_fft 已经包含了 Y_lagrange_U / rho_U 的影响
            % 分母也应包含 rho_U (如果 u_k 更新的目标函数包含 ||signal - sum(u_k)||^2 项的二次增广)
            % 在原始VMD论文的ADMM解法中，u的更新不直接包含rho_U，而是通过 f_hat - sum_others + lambda_hat/2
            % 这里的实现是ADMM框架，rho_U体现在 target_signal_for_vmd 中
            % 但VMD本身的alpha_vmd也起到了类似罚的作用
            % 我们需要确认这里的 rho_U 是否应该显式加入分母，
            % 如果 target_signal_for_vmd 已经是 (signal_orig + Y/rho_U), 
            % 那么 u_k 的更新目标是 minimize |F(u_k) - (target_fft - F(sum_{i!=k} u_i))|^2 + alpha_vmd * |(jw-jwk)F(u_k)|^2
            % 这个结构是正确的。

            % 加入相位引导项 (可选)
            if alpha_phase_guidance > 0 && ~isempty(phase_models_k) && ...
               size(phase_models_k,1) >= k_idx && any(phase_models_k(k_idx,:)) && ...
               ~all(isnan(phase_models_k(k_idx,:)))
                
                phase_prior_signal_time = exp(1j * phase_models_k(k_idx,:));
                phase_prior_term_fft = fft(phase_prior_signal_time); % 这是一个谱峰形状的先验
                
                % 将相位先验作为对目标谱的修正或附加项
                % 这里假设引导项是加性的，权重为 alpha_phase_guidance * alpha_vmd
                % (alpha_vmd也作为权重的一部分，可以调整)
                numerator_fft = numerator_fft + alpha_phase_guidance * alpha_vmd * phase_prior_term_fft;
                denominator = denominator + alpha_phase_guidance * alpha_vmd; % 分母也对应增加
            end
            
            u_k_fft(k_idx,:) = numerator_fft ./ denominator;

            % 更新 omega_k (中心频率)
            power_spectrum_uk = abs(u_k_fft(k_idx,:)).^2;
            if sum(power_spectrum_uk) > 1e-12 % 避免除以零
                % omega_k(k_idx) = sum(f_axis_normalized .* power_spectrum_uk) / sum(power_spectrum_uk);
                % 考虑频率轴的周期性，使用加权循环平均
                angles = 2 * pi * f_axis_normalized;
                weighted_mean_angle = angle(sum(power_spectrum_uk .* exp(1j * angles)));
                omega_k(k_idx) = mod(weighted_mean_angle / (2*pi), 1);
                if omega_k(k_idx) < 0, omega_k(k_idx) = omega_k(k_idx) + 1; end
            end
        end % loop K_vmd
        
        % VMD 内部收敛判断 (基于模态和的变化)
        if iter_inner > 1
            current_sum_fft = sum(u_k_fft,1);
            change_sum_fft = norm(current_sum_fft - u_sum_fft_prev_iter_vmd_loop) / (norm(u_sum_fft_prev_iter_vmd_loop) + eps);
            if change_sum_fft < tol_vmd_inner
                %fprintf('    VMD inner loop converged at iter %d, change: %e\n', iter_inner, change_sum_fft);
                break;
            end
            u_sum_fft_prev_iter_vmd_loop = current_sum_fft;
        else
             u_sum_fft_prev_iter_vmd_loop = sum(u_k_fft,1);
        end
    end % VMD inner loop
    
    u_k_updated = ifft(u_k_fft, [], 2); % 转换回时域
    omega_k_updated = omega_k;
end

function phase_poly = construct_phase_poly(tm_normalized, coeffs, PRF)
    % tm_normalized: 归一化慢时间 [0, 1)
    % coeffs: [fd_norm, ka_norm, kb_norm, ...] 归一化相位系数
    % PRF: 脉冲重复频率, 用于将归一化系数转换为实际相位贡献 (可选)
    %
    % Phase model: phi(t) = 2*pi * (fd*t + 0.5*ka*t^2 + (1/6)*kb*t^3 + ...)
    % where t is actual slow time.
    % If tm_normalized = t_actual / T_obs, and T_obs = N_azimuth / PRF
    % Then t_actual = tm_normalized * N_azimuth / PRF
    %
    % fd_norm = fd_actual / PRF  (Doppler freq normalized by PRF)
    % ka_norm = ka_actual / PRF^2 (Chirp rate normalized by PRF^2)
    % kb_norm = kb_actual / PRF^3 (Second order chirp rate normalized by PRF^3)
    %
    % So, phi(t_norm) = 2*pi * ( (fd_norm*PRF)*(t_norm*N/PRF) +
    %                            0.5*(ka_norm*PRF^2)*(t_norm*N/PRF)^2 +
    %                            (1/6)*(kb_norm*PRF^3)*(t_norm*N/PRF)^3 )
    %                  = 2*pi * ( fd_norm*t_norm*N +
    %                             0.5*ka_norm*(t_norm*N)^2 / N +  <-- Correction needed here
    %                             (1/6)*kb_norm*(t_norm*N)^3 / N^2 )
    %
    % Let's use the definition from many papers where normalized time t_n is in [-0.5, 0.5] or [0,1]
    % and coefficients are directly for this normalized time.
    % If tm_normalized is [0, (N-1)/N], corresponding to t_actual = m * Ts (m=0..N-1)
    % Phase: phi(m) = c0 + c1*m + c2*m^2 + c3*m^3 ...
    % If coeffs are [fd_norm, ka_norm, kb_norm] for tm_normalized in [0,1)
    % phi(t_norm) = 2*pi * (fd_norm * t_norm + 0.5 * ka_norm * t_norm^2 + (1/6) * kb_norm * t_norm^3)
    % This is a common way to define it if coeffs are already scaled for normalized time.
    % The original code uses this form. Let's stick to it.
    % The PRF is not strictly needed here if coeffs are already appropriately scaled "normalized" parameters.
    % However, the search for ka_norm and kb_norm in update_phase_coeffs_admm_enhanced
    % uses heuristics involving PRF, implying the coeffs might have some physical scaling.
    % The original `construct_phase_poly` did not use PRF.
    % The key is consistency between coefficient estimation and phase construction.
    % Given the coefficient estimation heuristics, it's safer to assume coeffs are for normalized time [0,1)
    % and the 2*pi factor is standard.

    poly_order = length(coeffs);
    phase_poly = zeros(size(tm_normalized));
    
    % Order 1: Doppler (fd_norm * t_norm)
    if poly_order >= 1 && ~isnan(coeffs(1))
        phase_poly = phase_poly + 2*pi * coeffs(1) * tm_normalized;
    end
    % Order 2: Linear Chirp Rate (0.5 * ka_norm * t_norm^2)
    if poly_order >= 2 && ~isnan(coeffs(2))
        phase_poly = phase_poly + 2*pi * 0.5 * coeffs(2) * tm_normalized.^2;
    end
    % Order 3: Quadratic Chirp Rate ((1/6) * kb_norm * t_norm^3)
    if poly_order >= 3 && ~isnan(coeffs(3))
        phase_poly = phase_poly + 2*pi * (1/6) * coeffs(3) * tm_normalized.^3;
    end
    % Add higher orders if poly_order > 3
    if poly_order >= 4 && ~isnan(coeffs(4)) % Example for 4th order
        phase_poly = phase_poly + 2*pi * (1/24) * coeffs(4) * tm_normalized.^4; % Factorial term
    end
end

function y = soft_threshold(x, threshold_val)
    y = sign(x) .* max(abs(x) - threshold_val, 0);
end


% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%                       仿真数据生成函数 (来自用户提供)
% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function [s_r_tm2, sim_params] = generate_simulated_echo()
    % --- Scatterer Positions (Reduced Scale from Original) ---
    Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
           0 -1 0;...
           1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
           -9.5 0.2 0.5;...
           -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
           0 1 0;...
           1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
           10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;...
           9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;...
           5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...
           5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;...
           0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;...
           -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;...
           -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...
           ];
    x_Pos_orig = Pos(:,1)*0.5; y_Pos_orig = Pos(:,2)*0.5; z_Pos_orig = Pos(:,3)*0.5;
    x_Pos = x_Pos_orig; y_Pos = y_Pos_orig; z_Pos = z_Pos_orig;

    % --- Radar Line of Sight (LOS) ---
    R_los = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)]; % Example LOS

    Num_point = size(x_Pos, 1);

    % --- Projection of Scatterer Positions for Rotation (Lever Arm Effect) ---
    x_r_proj = zeros(1,Num_point); y_r_proj = zeros(1,Num_point); z_r_proj = zeros(1,Num_point);
    for n_point = 1:Num_point
        x_r_proj(n_point) = y_Pos(n_point)*R_los(3) - z_Pos(n_point)*R_los(2);
        y_r_proj(n_point) = z_Pos(n_point)*R_los(1) - x_Pos(n_point)*R_los(3);
        z_r_proj(n_point) = x_Pos(n_point)*R_los(2) - y_Pos(n_point)*R_los(1);
    end

    % --- Rotational Motion Parameters (Angular Velocities, Accelerations, Jerks) ---
    % These define the *components* of angular velocity etc. that contribute to LOS velocity
    x_omega = 0.25; y_omega = 0.40; z_omega = 0.15; % Effective angular velocities
    x_alpha_rot = 0.15; y_alpha_rot = 0.25; z_alpha_rot = 0.10; % Effective angular accelerations
    x_beta_rot = 0.10; y_beta_rot = 0.15; z_beta_rot = 0.05; % Effective angular jerks

    % --- LOS Velocity Coefficients for Each Scatterer ---
    f_v_coeffs = zeros(1,Num_point);     % v_los = f_v_coeff (constant LOS velocity component)
    alpha_v_coeffs = zeros(1,Num_point); % dv_los/dt = alpha_v_coeff (LOS acceleration component)
    beta_v_coeffs = zeros(1,Num_point);  % d^2v_los/dt^2 = beta_v_coeff (LOS jerk component)

    for n_point = 1:Num_point
        f_v_coeffs(n_point) = x_r_proj(n_point)*x_omega + y_r_proj(n_point)*y_omega + z_r_proj(n_point)*z_omega;
        alpha_v_coeffs(n_point) = x_r_proj(n_point)*x_alpha_rot + y_r_proj(n_point)*y_alpha_rot + z_r_proj(n_point)*z_alpha_rot;
        beta_v_coeffs(n_point) = x_r_proj(n_point)*x_beta_rot + y_r_proj(n_point)*y_beta_rot + z_r_proj(n_point)*z_beta_rot;
    end

    % --- Simulation Parameters (Radar System) ---
    sim_params = struct();
    sim_params.B = 80*1e6;    % Bandwidth (Hz)
    sim_params.c = 3e8;       % Speed of light (m/s)
    sim_params.PRF = 1400;    % Pulse Repetition Frequency (Hz)
    sim_params.fc = 5.2e9;    % Carrier frequency (Hz)

    delta_r_res = sim_params.c / (2*sim_params.B); % Range resolution
    num_r_bins_sim = 128;                          % Number of range bins
    max_dist_sim = (num_r_bins_sim/2) * delta_r_res; % Max unambiguous range extent shown
    sim_params.r_axis = linspace(-max_dist_sim, max_dist_sim - delta_r_res, num_r_bins_sim);
    sim_params.Num_r = length(sim_params.r_axis);

    num_tm_sim = 256; % Number of slow time samples (pulses)
    sim_params.T_obs = (num_tm_sim-1)/sim_params.PRF; % Total observation time
    sim_params.tm = (0 : (1/sim_params.PRF) : sim_params.T_obs ); % Slow time axis
    sim_params.Num_tm = length(sim_params.tm);

    % --- Echo Generation ---
    s_r_tm2 = zeros(sim_params.Num_r, sim_params.Num_tm, 'like', 1j);
    Delta_R0_init = zeros(1,Num_point); % Initial LOS distance for each scatterer

    fprintf('  逐散射点生成回波 (仿真)...\n');
    ones_r_vec = ones(1, sim_params.Num_r);
    ones_tm_vec = ones(1, sim_params.Num_tm);

    for n_point = 1:Num_point
        % Initial LOS distance to scatterer (relative to scene center)
        Delta_R0_init(n_point) = x_Pos(n_point)*R_los(1) + y_Pos(n_point)*R_los(2) + z_Pos(n_point)*R_los(3);

        % Time-varying LOS distance (Range History)
        % R(t) = R0 + v*t + 0.5*a*t^2 + (1/6)*j*t^3
        Delta_R_t = Delta_R0_init(n_point) + ...
                    f_v_coeffs(n_point).*sim_params.tm + ...
                    (1/2)*alpha_v_coeffs(n_point).*sim_params.tm.^2 + ...
                    (1/6)*beta_v_coeffs(n_point).*sim_params.tm.^3;

        % Phase term
        phase_term = (4*pi*sim_params.fc/sim_params.c) * Delta_R_t; % 2*k0*R(t)

        % Amplitude factor (can vary per scatterer)
        amplitude_factor = 1.0 + 0.1*randn();
        if n_point > 20 && n_point < 30, amplitude_factor = 1.8 + 0.1*randn(); end % Stronger scatterers

        % Range profile (sinc function for each pulse)
        % (sim_params.r_axis.' * ones_tm_vec) creates a matrix where each col is r_axis
        % (ones_r_vec.' * Delta_R_t) creates a matrix where each row is Delta_R_t
        range_profiles = sinc((2*sim_params.B/sim_params.c) * (sim_params.r_axis.' * ones_tm_vec - ones_r_vec.' * Delta_R_t));

        s_r_tm2 = s_r_tm2 + amplitude_factor * range_profiles .* exp(1j * ones_r_vec.' * phase_term);
    end

    % --- Add Noise ---
    snr_db = 25; % Signal-to-noise ratio in dB (Increased for better visualization of algorithm performance)
    signal_power_total = sum(abs(s_r_tm2(:)).^2) / numel(s_r_tm2); % Average power per sample
    % signal_power = mean(abs(s_r_tm2(s_r_tm2~=0)).^2); % Power of non-zero signal components
    if signal_power_total < eps, signal_power_total = 1; end % Avoid log(0) or division by zero
    noise_power_total = signal_power_total / (10^(snr_db/10));
    noise = sqrt(noise_power_total/2) * (randn(size(s_r_tm2)) + 1j*randn(size(s_r_tm2)));
    s_r_tm2 = s_r_tm2 + noise;

    fprintf('  所有散射点回波生成完毕 (仿真)。\n');
end

% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%                       图像质量评估函数 (来自用户提供)
% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function contrast = calculate_image_contrast(image_abs)
    if isempty(image_abs) || numel(image_abs) < 2, contrast = 0; return; end
    image_abs_valid = image_abs(~isnan(image_abs) & ~isinf(image_abs));
    if isempty(image_abs_valid) || numel(image_abs_valid) < 2, contrast = 0; return; end
    mean_val = mean(image_abs_valid(:)); std_val = std(image_abs_valid(:));
    if abs(mean_val) < eps, contrast = 0; else, contrast = std_val / mean_val; end
end

function entropy = calculate_image_entropy(image_abs)
    if isempty(image_abs), entropy = NaN; return; end
    image_abs_valid = image_abs(~isnan(image_abs) & ~isinf(image_abs));
    if isempty(image_abs_valid), entropy = NaN; return; end
    image_power = image_abs_valid(:).^2; sum_power = sum(image_power);
    if sum_power < eps, entropy = 0; return; end
    normalized_power = image_power / sum_power;
    valid_indices = normalized_power > eps;
    if ~any(valid_indices), entropy = 0; return; end
    entropy = -sum(normalized_power(valid_indices) .* log2(normalized_power(valid_indices)));
end
