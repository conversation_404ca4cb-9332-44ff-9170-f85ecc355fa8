% test_zero_doppler_suppression.m
% 测试零多普勒抑制效果的脚本

clear; close all; clc;
fprintf('测试零多普勒抑制效果...\n');

% 加载数据
try
    load shipx2.mat; 
    load s_r_tm2.mat
    echo_data = s_r_tm2;
    fprintf('数据加载成功。\n');
catch
    fprintf('数据加载失败，退出测试。\n');
    return;
end

% 保存原始数据
echo_data_original = echo_data;

% 数据预处理（去均值）
fprintf('应用去均值预处理...\n');
for r_bin = 1:size(echo_data, 1)
    echo_data(r_bin, :) = echo_data(r_bin, :) - mean(echo_data(r_bin, :));
end

% 零多普勒抑制参数
zero_doppler_suppression = true;
dc_suppression_threshold = 0.2;
dc_suppression_factor = 0.05;

% 应用零多普勒抑制
if zero_doppler_suppression
    fprintf('应用零多普勒抑制...\n');
    suppression_count = 0;
    for r_bin = 1:size(echo_data, 1)
        signal_fft = fft(echo_data(r_bin, :));
        signal_fft_abs = abs(signal_fft);
        
        dc_idx = 1;
        total_energy = sum(signal_fft_abs.^2);
        dc_energy = signal_fft_abs(dc_idx)^2;
        
        if dc_energy > dc_suppression_threshold * total_energy
            suppression_count = suppression_count + 1;
            notch_width = 3;
            notch_indices = [dc_idx];
            if length(signal_fft) > notch_width
                notch_indices = [notch_indices, (length(signal_fft)-notch_width+2):length(signal_fft)];
            end
            
            signal_fft(notch_indices) = signal_fft(notch_indices) * dc_suppression_factor;
            echo_data(r_bin, :) = ifft(signal_fft);
        end
    end
    fprintf('对 %d 个距离单元应用了零多普勒抑制\n', suppression_count);
end

% 计算FFT结果进行对比
fprintf('计算FFT结果...\n');
raw_fft_original = fftshift(fft(echo_data_original, [], 2), 2);
raw_fft_processed = fftshift(fft(echo_data, [], 2), 2);

% 设置显示参数
sim_params = struct();
sim_params.Num_r = size(echo_data, 1);
sim_params.Num_tm = size(echo_data, 2);
sim_params.PRF = 1400;
sim_params.fc = 5.2e9;
sim_params.c = 3e8;
sim_params.B = 80e6;
delta_r_res_actual = sim_params.c / (2*sim_params.B);
r_center_actual = 0;
sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, ...
                             r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);
sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);

doppler_axis = linspace(-sim_params.PRF/2, sim_params.PRF/2, sim_params.Num_tm);

% 显示对比结果
fprintf('显示对比结果...\n');

% 原始FFT结果
figure('Name', '原始FFT结果');
G_original = 20*log10(abs(raw_fft_original)./max(abs(raw_fft_original(:)) + eps));
imagesc(doppler_axis, sim_params.r_axis, G_original);
caxis([-40,0]);
xlabel('多普勒频率 (Hz)');
ylabel('距离 (米)');
title('原始数据FFT结果 (dB)');
colorbar;
axis xy;
colormap('jet');

% 处理后FFT结果
figure('Name', '零多普勒抑制后FFT结果');
G_processed = 20*log10(abs(raw_fft_processed)./max(abs(raw_fft_processed(:)) + eps));
imagesc(doppler_axis, sim_params.r_axis, G_processed);
caxis([-40,0]);
xlabel('多普勒频率 (Hz)');
ylabel('距离 (米)');
title('零多普勒抑制后FFT结果 (dB)');
colorbar;
axis xy;
colormap('jet');

% 分析零频分量能量
fprintf('\n=== 零频分量能量分析 ===\n');
dc_energy_original = sum(abs(raw_fft_original(:, 1)).^2);
total_energy_original = sum(abs(raw_fft_original(:)).^2);
dc_ratio_original = dc_energy_original / total_energy_original;

dc_energy_processed = sum(abs(raw_fft_processed(:, 1)).^2);
total_energy_processed = sum(abs(raw_fft_processed(:)).^2);
dc_ratio_processed = dc_energy_processed / total_energy_processed;

fprintf('原始数据零频能量比例: %.4f (%.2f%%)\n', dc_ratio_original, dc_ratio_original*100);
fprintf('处理后零频能量比例: %.4f (%.2f%%)\n', dc_ratio_processed, dc_ratio_processed*100);
fprintf('零频能量抑制比例: %.2f%%\n', (1-dc_ratio_processed/dc_ratio_original)*100);

fprintf('测试完成。\n');
