% testgemi_aggressive_suppression.m
% 基于testgemi.m的激进零多普勒抑制版本

% 复制testgemi.m的内容并修改关键参数
clear; close all; clc;
fprintf('开始ISAR成像仿真与激进零多普勒抑制处理...\n');

% -------------------- 1. 数据加载与仿真参数 -------------------- %
fprintf('加载/生成雷达回波数据...\n');
tic;
try
    load shipx2.mat; 
    load s_r_tm2.mat
    echo_data = s_r_tm2;
    fprintf('实际数据 shipx2_1000.mat 加载成功。\n');
    sim_params = struct();
    sim_params.Num_r = size(echo_data, 1);
    sim_params.Num_tm = size(echo_data, 2);
    sim_params.PRF = 1400; 
    sim_params.fc = 5.2e9;  
    sim_params.c = 3e8;
    sim_params.B = 80e6;    
    delta_r_res_actual = sim_params.c / (2*sim_params.B);
    r_center_actual = 0; 
    sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, ...
                                 r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);
    sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);
catch
    fprintf('未找到 shipx2_1000.mat，生成仿真数据...\n');
    [echo_data, sim_params] = generate_simulated_echo(); 
end

% --- 数据预处理：去均值 ---
fprintf('对原始回波数据进行预处理 (去均值)...\n');
for r_bin = 1:size(echo_data, 1)
    echo_data(r_bin, :) = echo_data(r_bin, :) - mean(echo_data(r_bin, :));
end

% --- 激进的零多普勒抑制预处理 ---
fprintf('应用激进零多普勒抑制处理...\n');
zero_doppler_suppression = true;
center_suppression_threshold = 0.001; % 非常低的阈值
center_suppression_factor = 0.01; % 非常强的抑制
notch_width = 8; % 更宽的陷波

if zero_doppler_suppression
    suppression_count = 0;
    
    for r_bin = 1:size(echo_data, 1)
        signal_fft = fft(echo_data(r_bin, :));
        signal_fft_abs = abs(signal_fft);
        
        N = length(signal_fft);
        center_idx = round(N/2) + 1;
        
        center_range = max(1, center_idx-notch_width):min(N, center_idx+notch_width);
        center_energy = sum(signal_fft_abs(center_range).^2);
        total_energy = sum(signal_fft_abs.^2);
        
        if center_energy > center_suppression_threshold * total_energy
            suppression_count = suppression_count + 1;
            signal_fft(center_range) = signal_fft(center_range) * center_suppression_factor;
            echo_data(r_bin, :) = ifft(signal_fft);
        end
    end
    fprintf('对 %d 个距离单元应用了激进零多普勒抑制\n', suppression_count);
end

fprintf('数据加载/生成完毕。耗时: %.2f 秒\n', toc);
fprintf('回波数据尺寸: %d (距离单元) x %d (方位单元)\n', size(echo_data, 1), size(echo_data, 2));

% -------------------- 2. 设置处理参数 -------------------- %
params_proc = struct();
params_proc.vmd.K = 3;                  
params_proc.vmd.alpha_vmd = 2000;       
params_proc.vmd.tau_vmd = 0;            
params_proc.vmd.tol_vmd_inner = 1e-5;
params_proc.vmd.max_iter_vmd_inner = 20;
params_proc.vmd.init_omega_method = 'peaks_robust';
params_proc.vmd.alpha_phase_guidance = 0.5; 

params_proc.phase_est.poly_order = 3; 
params_proc.phase_est.fd_search_range_factor = 0.5; 
params_proc.phase_est.ka_search_pts = 31;    
params_proc.phase_est.kb_search_pts = 31;    
params_proc.phase_est.sharpness_weight = 0.05;
params_proc.phase_est.num_refinement_passes = 2;

params_proc.admm_global.rho_X = 1.0;      
params_proc.admm_global.rho_U = 0.5;      
params_proc.admm_global.lambda_sparsity = 0.03;
params_proc.admm_global.max_iter = 2;
params_proc.admm_global.tol = 1e-4;     

params_proc.apply_azimuth_window = true;
params_proc.zero_doppler_suppression = true;
params_proc.dc_suppression_threshold = 0.001; % 激进阈值
params_proc.dc_suppression_factor = 0.01; % 激进抑制

params_proc.num_azimuth = sim_params.Num_tm; 
params_proc.num_range_bins = sim_params.Num_r; 
params_proc.PRF = sim_params.PRF;
params_proc.fc = sim_params.fc;
params_proc.c = sim_params.c;
params_proc.tm_azimuth = sim_params.tm; 
params_proc.normalized_tm = (0:params_proc.num_azimuth-1) / params_proc.num_azimuth; 

% -------------------- 3. 简化的成像处理 -------------------- %
fprintf('开始简化的ISAR成像处理...\n');
tic;

% 直接应用FFT成像（跳过复杂的VMD-ADMM处理）
ISAR_image_simple = zeros(size(echo_data), 'like', 1j*echo_data(1));

% 应用窗函数
azimuth_window = hamming(params_proc.num_azimuth)';

for r_idx = 1:size(echo_data, 1)
    signal = echo_data(r_idx, :);
    
    % 再次应用零多普勒抑制
    signal_fft = fft(signal);
    signal_fft_abs = abs(signal_fft);
    N = length(signal_fft);
    center_idx = round(N/2) + 1;
    center_range = max(1, center_idx-5):min(N, center_idx+5);
    center_energy = sum(signal_fft_abs(center_range).^2);
    total_energy = sum(signal_fft_abs.^2);
    
    if center_energy > 0.001 * total_energy
        signal_fft(center_range) = signal_fft(center_range) * 0.005; % 极强抑制
    end
    
    signal = ifft(signal_fft);
    signal = signal - mean(signal);
    
    % 应用窗函数并FFT
    ISAR_image_simple(r_idx, :) = fft(signal .* azimuth_window);
end

fprintf('简化ISAR成像处理完毕。耗时: %.2f 秒\n', toc);

% -------------------- 4. 显示结果 -------------------- %
fprintf('分析成像结果...\n');

% 计算多普勒轴
doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, params_proc.num_azimuth);

% 应用fftshift
ISAR_image_simple_shifted = fftshift(ISAR_image_simple, 2);

% 分析中心频率能量
center_idx = round(size(ISAR_image_simple_shifted, 2) / 2);
center_range = max(1, center_idx-5):min(size(ISAR_image_simple_shifted, 2), center_idx+5);

center_energy = sum(abs(ISAR_image_simple_shifted(:, center_range)).^2, 'all');
total_energy = sum(abs(ISAR_image_simple_shifted(:)).^2, 'all');
center_ratio = center_energy / total_energy;

fprintf('\n=== 激进抑制效果分析 ===\n');
fprintf('中心频率能量比例: %.4f (%.2f%%)\n', center_ratio, center_ratio*100);

% 计算图像质量指标
contrast_val = std(abs(ISAR_image_simple_shifted(:))) / mean(abs(ISAR_image_simple_shifted(:)));
fprintf('图像对比度: %.4f\n', contrast_val);

% 保存结果
save('ISAR_results_aggressive.mat', 'ISAR_image_simple_shifted', 'sim_params', 'doppler_axis', 'center_ratio');
fprintf('激进抑制结果已保存到 ISAR_results_aggressive.mat\n');

fprintf('激进零多普勒抑制处理完成。\n');
